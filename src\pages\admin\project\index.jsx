import React, { useState, useEffect } from 'react';
import { Table, Button, Modal, Input, message, Switch } from 'antd';
import ProjectFormModal from './components/ProjectFormModal';
import defaultLogo from '../../../assets/images/default-logo.png';
import request from '@/utils/axios'; // 导入封装的request
import Cookies from 'js-cookie';
import Headers from '../../../layouts/header';
import 'antd/dist/antd.css';
import { debounce } from 'lodash';

const ProjectList = () => {
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(false);
  const [modalVisible, setModalVisible] = useState(false);
  const [editData, setEditData] = useState(null);
  const [total, setTotal] = useState(0);
  const [current, setCurrent] = useState(1);
  const [pageSize, setPageSize] = useState(10);

  // 获取项目列表数据
  const fetchProjects = async (params = {}) => {
    setLoading(true);
    try {
      const response = await request('/company/queryByPage', {
        method: 'GET',
        params: {
          pageNum: params.current || current,
          pageSize: params.pageSize || pageSize,
          name: params.name,
        },
      });

      if (response && response.code === 200) {
        setProjects(response.data.dataSources || []);
        setTotal(response.data.total);
        // setCurrent(response.data.pageNum);
        // setPageSize(response.data.pageSize);
      } else {
        message.error(response?.msg || '获取数据失败');
      }
    } catch (error) {
      console.error('获取项目列表失败:', error);
      message.error('获取项目列表失败');
    }
    setLoading(false);
  };

  useEffect(() => {
    fetchProjects();
  }, []);

  // 修改处理表格分页变化的方法
  const handleTableChange = (pagination, filters, sorter) => {
    // 更新当前页码和每页条数
    setCurrent(pagination.current);
    setPageSize(pagination.pageSize);
    
    fetchProjects({
      current: pagination.current,
      pageSize: pagination.pageSize,
      ...filters,
      ...sorter,
    });
  };

  // 添加防抖的搜索处理函数
  const debouncedSearch = debounce((value) => {
    setCurrent(1);
    fetchProjects({
      current: 1,
      name: value,
    });
  }, 500); // 500ms 的防抖延迟

  // 处理编辑
  const handleEdit = record => {
    console.log('record', record);
       setEditData(record); 
        setModalVisible(true);
  };

  // 处理删除
  const handleDelete = async id => {
    Modal.confirm({
      title: '确认删除',
      content: '确定要删除这个项目吗？',
      okText: '确认',
      okType: 'danger',
      cancelText: '取消',
      onOk: async () => {
        try {
          const response = await request(`/company/delete`, {
            method: 'POST',
            body: {
              id: id,
            },
          });

          if (response && response.code === 200) {
            message.success('删除成功');
            fetchProjects({
              current: current,
              pageSize: pageSize
            });
          } else {
            message.error(response?.msg || '删除失败');
          }
        } catch (error) {
          console.error('删除项目失败:', error);
          message.error('删除失败');
        }
      },
    });
  };

  // 处理需求用例视图开关
  const handleToggleRequirementView = async (checked, id) => {
    try {
      // 这里预留接口调用逻辑
      // 接口还未实现，先模拟成功响应并给出提示
      message.success(`${checked ? '启用' : '禁用'}需求用例视图${checked ? '成功' : '成功'}`);
      
      // 接口实现后的调用逻辑示例：
      /*
      const response = await request('/company/toggleRequirementView', {
        method: 'POST',
        body: {
          id: id,
          enableRequirementView: checked ? 1 : 0,
        },
      });

      if (response && response.code === 200) {
        message.success(`${checked ? '启用' : '禁用'}需求用例视图成功`);
        fetchProjects({
          current: current,
          pageSize: pageSize
        });
      } else {
        message.error(response?.msg || `${checked ? '启用' : '禁用'}需求用例视图失败`);
      }
      */
      
      // 临时更新本地状态，实际应该通过重新获取数据来更新
      setProjects(prevProjects => 
        prevProjects.map(project => 
          project.id === id ? { ...project, enableRequirementView: checked ? 1 : 0 } : project
        )
      );
    } catch (error) {
      console.error('切换需求用例视图状态失败:', error);
      message.error('操作失败');
    }
  };

  // 处理表单提交（新增/编辑）
  const handleFormSubmit = async (values) => {
    try {
      const url = editData ? `/company/update` : '/company/insert';
      const method = editData ? 'POST' : 'POST';

      const submitData = editData ? {
        id: editData.id,
        ...values,
      } : {
        ...values,
      };

      const response = await request(url, {
        method,
        body: submitData
      });

      if (response && response.code === 200) {
        message.success(`${editData ? '编辑' : '新增'}成功`);
        // 只有在成功时才清空和关闭表单
        setModalVisible(false);
        setEditData(null);
        fetchProjects({
          current: current,
          pageSize: pageSize
        });
      } else {
        // 失败时不关闭表单，让用户可以修改后重试
        message.error(response?.msg || `${editData ? '编辑' : '新增'}失败`);
      }
    } catch (error) {
      console.error(`${editData ? '编辑' : '新增'}项目失败:`, error);
      message.error(`${editData ? '编辑' : '新增'}失败`);
      // 发生错误时也不关闭表单
    }
  };
  // 搜索栏样式
  const searchBarStyle = {
    background: '#fff',
    padding: '24px',
    marginBottom: '16px',
    borderRadius: '4px',
  };

  // 表格区域样式
  const tableAreaStyle = {
    background: '#fff',
    padding: '24px',
    borderRadius: '4px',
    height: '800px',
    overflow: 'hidden',
  };
  const columns = [
    {
      title: '序号',
      dataIndex: 'index',
      render: (_, __, index) =>
        (current - 1) * pageSize + index + 1,
    },
    {
      title: '项目名称',
      dataIndex: 'name',
    },
    {
      title: '项目图标',
      dataIndex: 'logo',
      render: icon => (
        <img
          src={icon || defaultLogo}
          alt="项目图标"
          style={{ width: 40, height: 40, objectFit: 'contain' }}
          onError={e => {
            e.target.onerror = null;
            e.target.src = defaultLogo;
          }}
        />
      ),
    },
    {
      title: '项目描述',
      dataIndex: 'remark',
    },
    // {
    //   title: '创建人',
    //   dataIndex: 'creator',
    // },
    {
      title: '创建时间',
      dataIndex: 'gmtCreated',
    },
    {
      title: '是否启用需求用例视图',
      dataIndex: 'enableRequirementView',
      render: (enableRequirementView, record) => (
        <Switch
          checked={enableRequirementView === 1}
          onChange={(checked) => handleToggleRequirementView(checked, record.id)}
        />
      ),
    },
    {
      title: '操作',
      key: 'action',
      render: (_, record) => (
        <div style={{ display: 'flex', gap: '8px' }}>
          <Button
            type="link"
            onClick={() => {
              handleEdit(record);
            }}
            style={{ padding: '4px 8px' }}
          >
            编辑
          </Button>
          <Button
            type="link"
            onClick={() => handleDelete(record.id)}
            style={{ padding: '4px 8px', color: '#ff4d4f' }}
          >
            删除
          </Button>
        </div>
      ),
    },
  ];

  return (
    <section style={{ marginBottom: 30 }}>
      {/* <Headers /> */}
    <div style={{ padding: '24px', background: '#f0f2f5',paddingBottom: '0px' }}>
      {/* 搜索区域 */}
      <div style={searchBarStyle}>
        <div style={{ display: 'flex', alignItems: 'center' }}>
          <Input
            placeholder="项目名称"
            style={{ width: 240, marginRight: 16 }}
            onChange={(e) => debouncedSearch(e.target.value)}
            allowClear
          />
        </div>
      </div>

      {/* 表格区域 */}
      <div style={tableAreaStyle}>
        <div
          style={{
            marginBottom: 16,
            display: 'flex',
            justifyContent: 'space-between',
          }}
        >
          <Button
            type="primary"
            icon="plus"
            onClick={() => {
              setEditData(null);
              setModalVisible(true);
            }}
          >
            新增项目
          </Button>
          <Button onClick={() => {
            setCurrent(1);
            fetchProjects({ current: 1, pageSize });
          }}>刷新</Button>
        </div>

        <Table
          columns={columns}
          dataSource={projects}
          rowKey="id"
          loading={loading}
          pagination={{
            current: current,
            pageSize: pageSize,
            total: total,
            showQuickJumper: true,
            showSizeChanger: true,
            showTotal: total => `共 ${total} 条`,
          }}
          onChange={handleTableChange}
          scroll={{ y: 550 }}  // 直接设置表格的滚动高度为600px
          style={{ height: 550 }}  // 设置表格整体高度
        />
      </div>

      <ProjectFormModal
        visible={modalVisible}
        editData={editData}
        onCancel={() => {
          setModalVisible(false);
          setEditData(null);
        }}
        onOk={handleFormSubmit}
      />
    </div>
    </section>
  );
};

export default ProjectList;
