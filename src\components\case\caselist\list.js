/* eslint-disable */
import React from 'react';
import Link from 'umi/link';
import router from 'umi/router';
import request from '@/utils/axios';
import {
  Table,
  Pagination,
  message,
  Modal,
  Checkbox,
  Icon,
  Menu,
  Dropdown,
  Tooltip,
} from 'antd';
import './index.scss';
import moment from 'moment';
import { getRequirmentAllInfos } from '@/utils/requirementUtils.js';
moment.locale('zh-cn');
import _ from 'lodash';
import PropTypes from 'prop-types';
import TaskModal from './taskModal';
import InitiateReviewModal from './InitiateReviewModal';
import getQueryString from '@/utils/getCookies';
const getCookies = getQueryString.getCookie;
import debounce from 'lodash/debounce';
class Lists extends React.Component {
  static contextTypes = {
    router: PropTypes.object,
  };
  constructor(props) {
    super(props);
    this.state = {
      list: this.props.list,
      total: 0, // 数据条数
      current: this.props.current || 1, // 当前第几页
      // pageSize: this.props.pageSize || 10, // 添加默认每页显示条数
      choiseDate: [], // 时间筛选最终选择
      iterationFilter: '', // 需求筛选最终选择
      createrFilter: '', // 创建人筛选最终选择
      nameFilter: '', // 用例名称筛选最终选择
      caseFile: null, // 保存上传的file文件，单文件    };
      checked: false,
      requirementIds: [],
      requirementObj: [],
      taskVisible: false,
      record: null,
      extRecord: null,
      expendKeys: [],
      titleModeTask: '',
      loading: this.props.loading,
      extendLoading: new Map(),
      caseInfo: {},
      ownerList: [],
      fetching: false,
      requirementSeach: '',
      initiateReviewVisible: false,
      selectedRecord: null,
      // selectedRows: [],
    };
    this.lastFetchId = 0;
    this.getOwnerList = debounce(this.getOwnerList, 800);
  }

  componentWillReceiveProps(nextProps) {
    if (this.props.list != nextProps.list) {
      this.setState({ list: nextProps.list }, () => {
        this.setState({
          loading: nextProps.loading,
          current: this.props.current,
          choiseDate: this.props.choiseDate,
          iterationFilter: this.props.iterationFilter,
          createrFilter: this.props.createrFilter,
          nameFilter: this.props.nameFilter,
          caseKeyWords: this.props.caseKeyWords,
          expendKeys: [],
        });
      });
    }
  }
  delOk = record => {
    let { getTreeList } = this.props;
    let url = `${this.props.doneApiPrefix}/case/delete`;

    let params = {
      id: record.id,
    };
    request(url, {
      method: 'POST',
      body: params,
    }).then(res => {
      if (res.code === 200) {
        message.success('删除成功');
        // this.props.getCaseList(this.state.current, '', '', '', '');
        getTreeList();
        this.setState({ checked: false });
      } else {
        message.error(res.msg);
      }
      return null;
    });
  };

  onChangeCheckbox = e => {
    this.setState({ checked: e.target.checked });
  };

  setColumns = () => {
    const { isDeleteFolder, selectedRows, onSelectedRowsChange, viewMode = 'default' } = this.props;

    // 如果是需求视图，返回需求视图的列配置
    if (viewMode === 'requirement') {
      return this.getRequirementViewColumns();
    }

    // 默认视图的列配置
    const columns = [
      isDeleteFolder
        ? {
            title: (
              <Checkbox
                checked={
                  this.props.list.length > 0 &&
                  selectedRows.length === this.props.list.length
                }
                indeterminate={
                  selectedRows.length > 0 &&
                  selectedRows.length < this.props.list.length
                }
                onChange={() => {
                  if (selectedRows.length < this.props.list.length) {
                    // 全选当前页
                    onSelectedRowsChange(this.props.list.map(item => item.id));
                  } else {
                    // 取消全选
                    onSelectedRowsChange([]);
                  }
                }}
              />
            ),
            dataIndex: 'select',
            width: '5%',
            render: (text, record) => (
              <Checkbox
                checked={selectedRows.includes(record.id)}
                onChange={() => {
                  if (selectedRows.includes(record.id)) {
                    onSelectedRowsChange(
                      selectedRows.filter(id => id !== record.id),
                    );
                  } else {
                    onSelectedRowsChange([...selectedRows, record.id]);
                  }
                }}
              />
            ),
          }
        : null,
      {
        title: '用例ID',
        dataIndex: 'id',
        key: 'id',
        width: '8%',
        render: text => <div style={{ minWidth: '70px' }}>{text}</div>,
      },
      {
        title: '用例名称',
        dataIndex: 'title',
        key: 'title',
        width: '20%',
        render: (text, record) => {
          // 删除文件夹不能打开
          if(this.props.isDeleteFolder || sessionStorage.getItem('selectedTreeNode')=='delete') {
            return <span style={{color:'#ff4d4f'}}>{text}</span>;
          }        
          let url = `${this.props.baseUrl}/caseManager/${this.props.productId}/${record.id}/undefined/0`;
          return <Link to={url}>{text}</Link>;
        },
      },
      {
        title: '关联需求',
        dataIndex: 'requirementId',
        key: 'requirementId',
        width: '15%',
        render: text => <div style={{ minWidth: '200px' }}>{text}</div>,
      },
      {
        title: '最近更新人',
        dataIndex: 'modifier',
        width: '10%',
        key: 'modifier',
      },
      {
        title: '创建人',
        dataIndex: 'creator',
        width: '7%',
        key: 'creator',
      },
      {
        title: '创建时间',
        dataIndex: 'gmtCreated',
        width: '15%',
        key: 'gmtCreated',
        render: text => {
          return (
            <div>
              <span>{moment(text).format('YYYY-MM-DD HH:mm:ss')}</span>
            </div>
          );
        },
      },
      {
        title: 'Review情况',
        dataIndex: 'reviewStatus',
        width: '10%',
        key: 'reviewStatus',
        render: (text, record) => {
          const statusMap = {
            0: '未review',
            1: 'review中',
            2: '已review'
          };

          if (text === 0) {
            return <span>{statusMap[text]}</span>;
          }

          const currentUser = getCookies('userName');
          const isReviewer = record.tips?.reviews.some(item => item.username === currentUser);
          const isReviser = record.tips?.revises.some(item => item.username === currentUser);

          const tipContent = (
            <div>
              <p>Review人: {record.tips?.reviews.map(item => item.username).join(',') || '-'}</p>
              <p>修订人: {record.tips?.revises.map(item => item.username).join(',') || '-'}</p>
              <div style={{ borderTop: '1px solid #e8e8e8', marginTop: '8px', paddingTop: '8px' }}>
                {text === 1 ? (
                  <>
                    {isReviewer&&record.actReviewStatus === 1 && (
                      <a 
                        style={{ marginRight: '8px' }}
                        onClick={() => {
                          router.push(`/review/caseReview?caseId=${record.id}&reviewId=${record.tips?.reviewId}`);
                        }}
                      >
                        开始review
                      </a>
                    )}
                    {isReviser && record.actReviewStatus === 2 && (
                      <a 
                        style={{ marginRight: '8px' }}
                        onClick={() => {
                          router.push(`/review/caseRevision?caseId=${record.id}&reviewId=${record.tips?.reviewId}`);
                        }}
                      >
                        开始修订
                      </a>
                    )}
                    {record.reviewStatus === 2 && <a
                      onClick={() => {
                        router.push(`/reviewContrast?caseId=${record.id}&reviewId=${record.tips?.reviewId}`);
                      }}
                    >
                      查看
                    </a>}
                  </>
                ) : (
                  <a
                    onClick={() => {
                      router.push(`/reviewContrast?caseId=${record.id}&reviewId=${record.tips?.reviewId}`);
                    }}
                  >
                    查看
                  </a>
                )}
              </div>
            </div>
          );

          return (
            <Tooltip 
              title={tipContent}
              overlayStyle={{ maxWidth: '400px' }}
            >
              <span style={{ cursor: 'pointer', color: text === 1 ? '#1890ff' : '#52c41a' }}>
                {statusMap[text]}
              </span>
            </Tooltip>
          );
        }
      },
      {
        title: '操作',
        dataIndex: 'handle',
        width: '15%',
        key: 'handle',
        render: (text, record) => {
          const { projectLs, requirementLs } = this.props.options;
          const { type } = this.props;

          let creator = getCookies('userName');
          let recordCreator = record.creator.match(/\(([^)]*)\)/)
            ? record.creator.match(/\(([^)]*)\)/)[1]
            : record.creator;
          if (this.props.isDeleteFolder || sessionStorage.getItem('selectedTreeNode')=='delete') {
            return (
              <Tooltip title="恢复用例">
                <a
                  onClick={() => {
                    // 单个恢复
                    this.props.onRestore(record.id);
                  }}
                  className="icon-bg"
                >
                  <Icon type="undo" />
                </a>
              </Tooltip>
            );
          } else {
            return (
              <span>
                <Tooltip title="发起Review">
                  <a
                    onClick={() => this.handleInitiateReview(record)}
                    className="icon-bg border-a-redius-left"
                  >
                    <Icon type="audit" />
                  </a>
                </Tooltip>
                <Tooltip title="编辑用例">
                  <a
                    onClick={() => {
                      let infos =
                        getRequirmentAllInfos(
                          projectLs,
                          requirementLs,
                          record.requirementId,
                        ) || {};
                      let project = infos.project || [];
                      let requirement = infos.requirement || [];
                      this.props.handleTask(
                        'edit',
                        record,
                        project,
                        requirement,
                        this.state.current,
                      );
                    }}
                    className="icon-bg"
                  >
                    <Icon type="edit" />
                  </a>
                </Tooltip>
                <Tooltip title="创建测试任务">
                  <a
                    className="icon-bg"
                    onClick={() => {
                      this.showTask('新建测试任务', record);
                    }}
                  >
                    <Icon type="file-done" />
                  </a>
                </Tooltip>
                <Tooltip title="复制用例">
                  <a
                    onClick={() => {
                      let infos =
                        getRequirmentAllInfos(
                          projectLs,
                          requirementLs,
                          record.requirementId,
                        ) || {};
                      let project = infos.project || [];
                      let requirement = infos.requirement || [];
                      this.props.handleTask(
                        'copy',
                        record,
                        project,
                        requirement,
                      );
                    }}
                    className="icon-bg border-a-redius-right margin-3-right"
                  >
                    <Icon type="copy" />
                  </a>
                </Tooltip>
                <Dropdown
                  overlay={
                    <Menu>
                      <Menu.Item>
                        <a
                          onClick={() => {
                            Modal.confirm({
                              title: '确认删除用例吗',
                              content: (
                                <span>
                                  当前正在删除&nbsp;&nbsp;
                                  <span style={{ color: 'red' }}>
                                    {record.title}
                                  </span>
                                  &nbsp;&nbsp;用例，并且删除用例包含的{' '}
                                  <span style={{ color: 'red' }}>
                                    {record.recordNum}
                                  </span>{' '}
                                  {/* 个测试任务与测试结果等信息，此操作不可撤销 */}
                                  个测试任务与测试结果等信息，此操作把数据转移到删除文件夹中,可在删除文件夹中找回~
                                  <br />
                                  <br />
                                  <Checkbox onChange={this.onChangeCheckbox}>
                                    我明白以上操作
                                  </Checkbox>
                                </span>
                              ),
                              onOk: e => {
                                if (this.state.checked) {
                                  this.delOk(record);
                                  Modal.destroyAll();
                                } else {
                                  message.info('请先勾选我已明白以上操作');
                                }
                              },
                              icon: <Icon type="exclamation-circle" />,
                              cancelText: '取消',
                              okText: '删除',
                            });
                          }}
                        >
                          删除
                        </a>
                      </Menu.Item>
                      <Menu.Item>
                        <a
                          onClick={e => {
                            e.preventDefault();
                            router.push(`/history/${record.id}`);
                          }}
                        >
                          历史版本
                        </a>
                      </Menu.Item>
                      <Menu.Item>
                        {/* <a
                            href={`/api/file/export?id=${record.id}`}
                            target="_blank"
                          >
                            导出xmind
                          </a> */}
                        <a
                          onClick={e => {
                            e.preventDefault();
                            const baseUrl =
                              process.env.NODE_ENV === 'development'
                                ? 'http://10.250.202.113:8443/thirdapp/ecocase'
                                : `${window.location.origin}/thirdapp/ecocase`;
                            const exportUrl = `${baseUrl}/api/file/export?id=${record.id}`;
                            // 直接下载文件
                            window.location.href = exportUrl;
                          }}
                        >
                          导出xmind
                        </a>
                      </Menu.Item>
                    </Menu>
                  }
                >
                  <a className="icon-bg border-around">
                    <Icon type="ellipsis" />
                  </a>
                </Dropdown>
              </span>
            );
          }
        },
      },
    ].filter(Boolean); // 过滤掉 null 值;
    return columns;
  };

  // 需求视图的列配置
  getRequirementViewColumns = () => {
    const { isDeleteFolder, selectedRows, onSelectedRowsChange } = this.props;
    const columns = [
      isDeleteFolder
        ? {
            title: (
              <Checkbox
                checked={
                  this.props.list.length > 0 &&
                  selectedRows.length === this.props.list.length
                }
                indeterminate={
                  selectedRows.length > 0 &&
                  selectedRows.length < this.props.list.length
                }
                onChange={() => {
                  if (selectedRows.length < this.props.list.length) {
                    // 全选当前页
                    onSelectedRowsChange(this.props.list.map(item => item.id));
                  } else {
                    // 取消全选
                    onSelectedRowsChange([]);
                  }
                }}
              />
            ),
            dataIndex: 'select',
            width: '5%',
            render: (text, record) => (
              <Checkbox
                checked={selectedRows.includes(record.id)}
                onChange={() => {
                  if (selectedRows.includes(record.id)) {
                    onSelectedRowsChange(
                      selectedRows.filter(id => id !== record.id),
                    );
                  } else {
                    onSelectedRowsChange([...selectedRows, record.id]);
                  }
                }}
              />
            ),
          }
        : null,
      {
        title: '需求单',
        dataIndex: 'requirementId',
        key: 'requirementId',
        width: '20%',
        render: text => (
          <div style={{ minWidth: '150px' }}>
            {text ? (
              <span className="requirement-id-cell">{text}</span>
            ) : (
              '-'
            )}
          </div>
        ),
      },
      {
        title: '用例更新时间',
        dataIndex: 'gmtModified',
        width: '15%',
        key: 'gmtModified',
        render: text => {
          return (
            <div>
              <span>{text ? moment(text).format('YYYY-MM-DD HH:mm:ss') : '-'}</span>
            </div>
          );
        },
      },
      {
        title: '测试用例',
        dataIndex: 'title',
        key: 'title',
        width: '25%',
        render: (text, record) => {
          // 删除文件夹不能打开
          if(this.props.isDeleteFolder || sessionStorage.getItem('selectedTreeNode')=='delete') {
            return <span style={{color:'#ff4d4f'}}>{text}</span>;
          }
          let url = `${this.props.baseUrl}/caseManager/${this.props.productId}/${record.id}/undefined/0`;
          return <Link to={url}>{text}</Link>;
        },
      },
      {
        title: '负责QA',
        dataIndex: 'qaOwner',
        width: '10%',
        key: 'qaOwner',
        render: text => (
          <div className="qa-owner-cell">{text || '-'}</div>
        ),
      },
      {
        title: '用例状态',
        dataIndex: 'caseStatus',
        width: '10%',
        key: 'caseStatus',
        render: text => {
          const statusMap = {
            0: '未上传',
            1: '待Review',
            2: 'Review中',
            3: '已Review',
            4: '已修订'
          };
          return (
            <span className={`case-status-cell status-${text || 0}`}>
              {statusMap[text] || '未知状态'}
            </span>
          );
        },
      },
      {
        title: '操作',
        dataIndex: 'handle',
        width: '15%',
        key: 'handle',
        render: (text, record) => this.renderRequirementViewActions(record),
      },
    ].filter(Boolean); // 过滤掉 null 值
    return columns;
  };

  // 需求视图的操作按钮渲染
  renderRequirementViewActions = (record) => {
    if (this.props.isDeleteFolder || sessionStorage.getItem('selectedTreeNode')=='delete') {
      return (
        <Tooltip title="恢复用例">
          <a
            onClick={() => {
              // 单个恢复
              this.props.onRestore(record.id);
            }}
            className="icon-bg"
          >
            <Icon type="undo" />
          </a>
        </Tooltip>
      );
    } else {
      return (
        <span>
          <Tooltip title="发起Review">
            <a
              onClick={() => this.handleInitiateReview(record)}
              className="icon-bg border-a-redius-left"
            >
              <Icon type="audit" />
            </a>
          </Tooltip>
          <Tooltip title="上传用例">
            <a
              onClick={() => this.handleUploadCase(record)}
              className="icon-bg"
            >
              <Icon type="upload" />
            </a>
          </Tooltip>
          <Tooltip title="忽略">
            <a
              onClick={() => this.handleIgnoreCase(record)}
              className="icon-bg border-a-redius-right margin-3-right"
            >
              <Icon type="eye-invisible" />
            </a>
          </Tooltip>
          <Dropdown
            overlay={
              <Menu>
                <Menu.Item>
                  <a
                    onClick={() => {
                      Modal.confirm({
                        title: '确认删除用例吗',
                        content: (
                          <span>
                            当前正在删除&nbsp;&nbsp;
                            <span style={{ color: 'red' }}>
                              {record.title}
                            </span>
                            &nbsp;&nbsp;用例，并且删除用例包含的{' '}
                            <span style={{ color: 'red' }}>
                              {record.recordNum}
                            </span>{' '}
                            个测试任务与测试结果等信息，此操作把数据转移到删除文件夹中,可在删除文件夹中找回~
                            <br />
                            <br />
                            <Checkbox onChange={this.onChangeCheckbox}>
                              我明白以上操作
                            </Checkbox>
                          </span>
                        ),
                        onOk: e => {
                          if (this.state.checked) {
                            this.delOk(record);
                            Modal.destroyAll();
                          } else {
                            message.info('请先勾选我已明白以上操作');
                          }
                        },
                        icon: <Icon type="exclamation-circle" />,
                        cancelText: '取消',
                        okText: '删除',
                      });
                    }}
                  >
                    删除
                  </a>
                </Menu.Item>
                <Menu.Item>
                  <a
                    onClick={e => {
                      e.preventDefault();
                      const baseUrl =
                        process.env.NODE_ENV === 'development'
                          ? 'http://10.250.202.113:8443/thirdapp/ecocase'
                          : `${window.location.origin}/thirdapp/ecocase`;
                      const exportUrl = `${baseUrl}/api/file/export?id=${record.id}`;
                      // 直接下载文件
                      window.location.href = exportUrl;
                    }}
                  >
                    导出xmind
                  </a>
                </Menu.Item>
              </Menu>
            }
          >
            <a className="icon-bg border-around">
              <Icon type="ellipsis" />
            </a>
          </Dropdown>
        </span>
      );
    }
  };

  // 上传用例处理方法
  handleUploadCase = (record) => {
    // TODO: 实现上传用例逻辑
    message.info('上传用例功能待实现');
  };

  // 忽略用例处理方法
  handleIgnoreCase = (record) => {
    // TODO: 实现忽略用例逻辑
    message.info('忽略用例功能待实现');
  };
  // 分页
  onChangePagination = (page, pageSize) => {
    this.setState({ current: page, expendKeys: [] }, () => {
      const {
        nameFilter,
        createrFilter,
        iterationFilter,
        choiseDate,
        caseKeyWords,
      } = this.state;
      // 调用父组件的方法，传递页码和页大小
    this.props.onPageChange(page, pageSize);
      this.props.getCaseList(
        this.state.current,
        nameFilter || '',
        createrFilter || '',
        iterationFilter || '',
        choiseDate || [],
        caseKeyWords || '',
        pageSize // 传递pageSize给getCaseList
      );
    });
  };
  // 处理页码大小改变的方法
  onShowSizeChange = (current, pageSize) => {
    this.setState({ current: 1 }, () => {
      const {
        nameFilter,
        createrFilter,
        iterationFilter,
        choiseDate,
        caseKeyWords,
      } = this.state;
  
      // 调用父组件的方法，传递页码和页大小
      this.props.onPageChange(1, pageSize);
      
      this.props.getCaseList(
        1,
        nameFilter || '',
        createrFilter || '',
        iterationFilter || '',
        choiseDate || [],
        caseKeyWords || '',
        pageSize
      );
      this.setState({
        current: 1,
        pageSize // 更新组件状态中的 pageSize
      });
    });
  }; 
  onCloseTask = form => {
    (this.state.ownerList = []), form.resetFields();
    this.setState({ taskVisible: false });
  };
  handleOkTask = record => {
    this.getRecordList(record.caseId || record.id);
    this.setState({
      taskVisible: false,
      expendKeys: [record.caseId || record.id],
    });
  };
  // priority 数据转换
  handleChooseContent = content => {
    let val = content && JSON.parse(content).priority;
    let val1 = val.indexOf('0') > -1 ? '0' : '1';
    return {
      content: val1,
      priority: val1 === '1' ? val : [],
    };
  };

  showTask = (title, record) => {
    let priority = record.chooseContent
      ? this.handleChooseContent(record.chooseContent).priority
      : [];
    let resource = record.chooseContent
      ? JSON.parse(record.chooseContent).resource
      : [];
    this.setState(
      { taskVisible: true, record: record, titleModeTask: title, caseInfo: {} },
      () => {
        this.getCaseInfo(priority, resource);
      },
    );
  };
  // 获取case信息
  getCaseInfo = (priority, resource) => {
    const { record, titleModeTask } = this.state;
    let url = `${this.props.doneApiPrefix}/case/countByCondition`;

    request(url, {
      method: 'GET',
      params: {
        caseId: titleModeTask === '编辑测试任务' ? record.caseId : record.id,
        priority,
        resource: resource || [],
      },
    }).then(res => {
      if (res.code === 200) {
        this.setState({ caseInfo: res.data });
      }
    });
  };
  renderExpand = item => {
    const columns = [
      {
        title: '任务ID',
        dataIndex: 'id',
        key: 'id',
      },
      {
        title: '任务名称',
        dataIndex: 'title',
        key: 'title',
        width: 200,
        render: (text, record) => {        
          if(this.props.isDeleteFolder || sessionStorage.getItem('selectedTreeNode')=='delete') {         
            return <span style={{color:'#ff4d4f'}}>{text}</span>;
          }
          let url = `${this.props.baseUrl}/caseManager/${this.props.productId}/${record.caseId}/${record.id}/3`;
          return (
            <Tooltip title={text}>
              <a
                onClick={() => this.taskLink(url, record)}
                className="table-ellipsis"
              >
                {text}
              </a>
            </Tooltip>
          );
        },
      },
      {
        title: () => (
          <Tooltip placement="top" title="负责执行任务与标记用例结果">
            <span style={{ cursor: 'pointer' }}>负责人</span>
          </Tooltip>
        ),
        dataIndex: 'owner',
        key: 'owner',
        render: text => (
          <Tooltip title={text}>
            <span className="table-ellipsis">{text}</span>
          </Tooltip>
        ),
      },
      {
        title: () => (
          <Tooltip placement="top" title="参与标记用例结果的人员列表">
            <span style={{ cursor: 'pointer' }}>执行人</span>
          </Tooltip>
        ),
        dataIndex: 'executors',
        key: 'executors',
        width: 100,
        render: text => (
          <Tooltip title={text}>
            <span className="table-ellipsis">{text}</span>
          </Tooltip>
        ),
      },
      {
        title: '通过率',
        dataIndex: 'successNum',
        key: 'successNum',
        align: 'center',
        render: (text, record) => (
          <span className="table-operation">
            {parseInt((text / record.totalNum) * 100)}%
          </span>
        ),
      },
      {
        title: '已测用例',
        dataIndex: 'executeNum',
        key: 'executeNum',
        align: 'center',
        render: (text, record) => (
          <span className="table-operation">
            {text} / {record.totalNum}
          </span>
        ),
      },
      {
        title: '期望时间',
        dataIndex: 'expectStartTime',
        key: 'expectStartTime',
        render: (text, record) =>
          text
            ? `${moment(text).format('YYYY-MM-DD')} 至 ${moment(
                record.expectEndTime,
              ).format('YYYY-MM-DD')}`
            : '',
      },
      {
        title: '操作',
        dataIndex: 'handle',
        key: 'handle',
        render: (text, record) => {
          if(this.props.isDeleteFolder || sessionStorage.getItem('selectedTreeNode')=='delete') {
            return null
          }
          let creator = getCookies('userName');
          let recordCreator = record.creator.match(/\(([^)]*)\)/)
            ? record.creator.match(/\(([^)]*)\)/)[1]
            : record.creator;
          let url = `${this.props.baseUrl}/caseManager/${this.props.productId}/${record.caseId}/${record.id}/3`;

          return (
            <span>
              <Tooltip title="编辑任务">
                <a
                  onClick={() => {
                    this.showTask('编辑测试任务', record);
                  }}
                  className="icon-bg border-a-redius-left"
                >
                  <Icon type="edit" />
                </a>
              </Tooltip>
              <Tooltip title="执行测试">
                <a
                  className="icon-bg"
                  onClick={() => this.taskLink(url, record)}
                >
                  <Icon type="file-done" />
                </a>
              </Tooltip>
              <Tooltip title={`删除任务`}>
                <a
                  onClick={() => {
                    Modal.confirm({
                      title: '确认删除测试任务吗',
                      content: (
                        <span>
                          这将删除该测试任务下所有的测试与测试结果等信息，并且不可撤销。{' '}
                          <br />
                          <Checkbox onChange={this.onChangeCheckbox}>
                            我明白以上操作
                          </Checkbox>
                        </span>
                      ),
                      onOk: e => {
                        if (this.state.checked) {
                          this.deleteRecordList(record);

                          Modal.destroyAll();
                        } else {
                          message.info('请先勾选我已明白以上操作');
                        }
                      },
                      icon: <Icon type="exclamation-circle" />,
                      cancelText: '取消',
                      okText: '删除',
                    });
                  }}
                  className="icon-bg border-a-redius-right margin-3-right"
                >
                  <Icon type="delete" />
                </a>
              </Tooltip>
            </span>
          );
        },
      },
    ];
    return (
      <div className="recordTable" style={{ width: '91%' }}>
        {item.recordList &&
          item.recordList.length > 0 &&
          ((
            <Table
              columns={columns}
              dataSource={item.recordList}
              pagination={false}
              loading={this.state.extendLoading.get(item.id)}
              rowKey="id"
              size="middle"
            />
          ) ||
            null)}
      </div>
    );
  };
  // 任务名称跳转
  taskLink = (url, record) => {
    let loginUser = getCookies('userName');
    if (record.owner === '' || record.owner.indexOf(loginUser) > -1) {
      router.push(url);
    } else {
      this.showConfirm(url);
    }
  };
  // 任务名称跳转、执行测试confirm弹框
  showConfirm = url => {
    return Modal.confirm({
      title: '您不是当前测试任务指派的负责人，确认要执行该任务？',
      onOk() {
        router.push(url);
      },
      onCancel() {},
      icon: <Icon type="question-circle" style={{ color: '#1890FF' }} />,
      cancelText: '取消',
      okText: '确认',
    });
  };
  getOwnerList = value => {
    if (!value) {
      return;
    }
    this.lastFetchId += 1;
    const fetchId = this.lastFetchId;
    this.setState({ requirementSeach: value, fetching: true });
    request(`${this.props.oeApiPrefix}/user/suggest`, {
      method: 'GET',
      params: {
        username: value,
        onlyEmployee: false,
      },
    }).then(res => {
      if (fetchId !== this.lastFetchId) {
        return;
      }
      //  if (res.code === 200) {
      this.setState({ ownerList: res ? res : [], fetching: false });
      //  }
    });
  };

  clearRequire = () => {
    this.setState({ requirementSeach: '' });
  };

  onExpand = (expanded, record) => {
    if (expanded) {
      this.setState({ record }, () => {});
    }
  };
  getRecordList = id => {
    let url = `${this.props.doneApiPrefix}/record/list`;

    request(url, { method: 'GET', params: { caseId: id } }).then(res => {
      if (res.code == 200) {
        let { list } = this.state;
        list.map(item => {
          if (item.id === id) {
            item.recordList = res.data;
            item.recordNum = res.data.length;
            if (item.recordNum === 0) {
              this.setState({ expendKeys: [] });
            }
          }
        });

        this.setState({ list }, () => {
          let extendLoading = this.state.extendLoading.set(id, false);

          this.setState({
            extendLoading,
          });
        });
      } else {
        message.error(res.msg);
      }
    });
  };

  // /record/delete
  deleteRecordList = record => {
    let url = `${this.props.doneApiPrefix}/record/delete`;

    request(url, { method: 'POST', body: { id: record.id } }).then(res => {
      if (res.code == 200) {
        this.getRecordList(record.caseId);
        this.setState({ checked: false });
        message.success(res.data);
      } else {
        message.error(res.msg);
      }
    });
  };
  seeDetail = props => {
    let { expendKeys } = this.state;

    if (expendKeys.length > 0) {
      if (
        expendKeys.some(item => {
          return item == props.record.id;
        })
      ) {
        expendKeys.map(item => {
          if (item == props.record.id) {
            expendKeys.splice(expendKeys.indexOf(item), 1);
          }
        });
      } else {
        expendKeys.push(props.record.id);
      }
    } else {
      expendKeys.push(props.record.id);
    }
    this.setState({ expendKeys }, () => {
      if (!props.expanded) {
        this.getRecordList(props.record.id);
      }
    });
  };
  handleInitiateReview = record => {
    this.setState({
      initiateReviewVisible: true,
      selectedRecord: record
    });
  };

  handleInitiateReviewCancel = () => {
    this.setState({
      initiateReviewVisible: false,
      selectedRecord: null
    });
  };

  handleInitiateReviewOk = values => {
    const { productId } = this.props;
    request(`/review/insert`, {
      method: 'POST',
      body: {
        ...values,
        productLineId: productId,
        channel: 1,
        companyId: productId
      }
    }).then(res => {
      if (res.code === 200) {
        message.success('发起Review成功');
        this.setState({
          initiateReviewVisible: false,
          selectedRecord: null
        });
        // 刷新列表
        const { nameFilter, createrFilter, iterationFilter, choiseDate, caseKeyWords, current } = this.state;
        this.props.getCaseList(current, nameFilter, createrFilter, iterationFilter, choiseDate, caseKeyWords);
      } else {
        message.error(res.msg || '发起Review失败');
      }
    });
  };
  render() {
    const {
      list,
      current,
      expendKeys,
      // loading,
      requirementSeach,
      fetching,
      ownerList,
      selectedRows,
    } = this.state;
    const { total, loading, isDeleteFolder,pageSize } = this.props;
    return (
      <div>
        <Table
          columns={this.setColumns()}
          dataSource={list}
          expandedRowRender={item => this.renderExpand(item)}
          className={`table-wrap ${this.props.viewMode === 'requirement' ? 'requirement-view' : ''}`}
          onExpand={this.onExpand}
          expandedRowKeys={expendKeys}
          rowKey="id"
          size="middle"
          loading={loading}
          pagination={false}
          scroll={{ y: 'calc(100vh - 200px)' }}
          expandIcon={props => {
            if (props.record.recordNum > 0) {
              if (!props.expanded) {
                return (
                  <div
                    role="button"
                    tabIndex="0"
                    className="ant-table-row-expand-icon ant-table-row-collapsed"
                    aria-label="展开行"
                    onClick={() => {
                      let extendLoading = this.state.extendLoading.set(
                        props.record.id,
                        true,
                      );

                      this.setState({ extendLoading });
                      this.seeDetail(props);
                    }}
                  ></div>
                );
              } else {
                return (
                  <div
                    role="button"
                    tabIndex="0"
                    className="ant-table-row-expand-icon ant-table-row-expanded"
                    aria-label="关闭行"
                    onClick={() => {
                      this.seeDetail(props);
                    }}
                  ></div>
                );
              }
            } else {
              return null;
            }
          }}
          footer={currentData => (
            <div style={{ height: '32px' }}>
              {
                <div
                  className="pagination"
                  style={{
                    display: total === 0 ? 'none' : 'block',
                    float: 'right',
                  }}
                >
                  <Pagination
                    onChange={this.onChangePagination}
                    current={current}
                    total={Number(total)}
                    // pageSize={10}
                    onShowSizeChange={this.onShowSizeChange}
                    pageSize={pageSize}
                    showSizeChanger
                    showTotal={(total) => `共 ${total} 条`}
                  />
                </div>
              }
            </div>
          )}
        />
        <TaskModal
          key="id"
          visible={this.state.taskVisible}
          caseInfo={this.state.caseInfo}
          onClose={this.onCloseTask}
          handleOkTask={this.handleOkTask}
          showTask={this.showTask}
          getOwnerList={this.getOwnerList}
          ownerList={ownerList}
          fetching={fetching}
          requirementSeach={requirementSeach}
          clearRequire={this.clearRequire}
          record={this.state.record}
          type={this.props.type}
          doneApiPrefix={this.props.doneApiPrefix}
          titleModeTask={this.state.titleModeTask}
          getCaseInfo={this.getCaseInfo}
        />
        {this.state.initiateReviewVisible && <InitiateReviewModal
          visible={this.state.initiateReviewVisible}
          onCancel={this.handleInitiateReviewCancel}
          onOk={this.handleInitiateReviewOk}
          record={this.state.selectedRecord}
          productLineId={this.props.productId}
        />}
      </div>
    );
  }
}
export default Lists;
