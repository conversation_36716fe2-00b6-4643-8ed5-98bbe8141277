const deleteFolderData = {
  code: 200,
  msg: '服务运行成功',
  data: {
    dataSources: [
      {
        id: 2231,
        title: '222',
        description: '',
        creator: 'Test',
        modifier: 'Test',
        gmtCreated: '2025-02-21T14:35:53.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: '',
        channel: 1,
        groupId: 1,
        recordNum: 0,
        companyId: 36,
      },
      {
        id: 2230,
        title: '111111111111111',
        description: '',
        creator: 'Test',
        modifier: 'Test',
        gmtCreated: '2025-02-21T13:10:14.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: '',
        channel: 1,
        groupId: 1,
        recordNum: 0,
        companyId: 36,
      },
      {
        id: 2226,
        title: '66666',
        description: '',
        creator: 'Test',
        modifier: 'Test',
        gmtCreated: '2025-02-18T13:08:19.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: '',
        channel: 1,
        groupId: 1,
        recordNum: 0,
        companyId: 36,
      },
      {
        id: 2225,
        title: '22222222',
        description: '',
        creator: 'Test',
        modifier: 'Test',
        gmtCreated: '2025-02-18T13:05:22.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: '',
        channel: 1,
        groupId: 1,
        recordNum: 0,
        companyId: 36,
      },
      {
        id: 2217,
        title: 'LZC',
        description: '',
        creator: 'Test',
        modifier: 'Test',
        gmtCreated: '2025-02-10T16:04:21.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: '',
        channel: 1,
        groupId: 1,
        recordNum: 0,
        companyId: 36,
      },
      {
        id: 2216,
        title: '222',
        description: '',
        creator: 'Test',
        modifier: 'Test',
        gmtCreated: '2025-02-08T11:37:06.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: '333',
        channel: 1,
        groupId: 1,
        recordNum: 0,
        companyId: 36,
      },
      {
        id: 2212,
        title: '11',
        description: '',
        creator: 'Test',
        modifier: 'Test',
        gmtCreated: '2025-02-07T16:12:43.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: '11',
        channel: 1,
        groupId: 1,
        recordNum: 2,
        companyId: 36,
      },
      {
        id: 22311,
        title: '222',
        description: '',
        creator: 'Test',
        modifier: 'Test',
        gmtCreated: '2025-02-21T14:35:53.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: '',
        channel: 1,
        groupId: 1,
        recordNum: 0,
        companyId: 36,
      },
      {
        id: 22301,
        title: '111111111111111',
        description: '',
        creator: 'Test',
        modifier: 'Test',
        gmtCreated: '2025-02-21T13:10:14.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: '',
        channel: 1,
        groupId: 1,
        recordNum: 0,
        companyId: 36,
      },
      
    ],
    total: 7,
  },
};

// 需求视图Mock数据
const requirementViewData = {
  code: 200,
  msg: '服务运行成功',
  data: {
    dataSources: [
      {
        id: 2231,
        title: '用户登录功能测试用例',
        description: '',
        creator: 'Test QA',
        modifier: 'Test QA',
        gmtCreated: '2025-02-21T14:35:53.000+0800',
        gmtModified: '2025-02-22T10:20:30.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: 'REQ-2024-001',
        channel: 1,
        groupId: 1,
        recordNum: 0,
        companyId: 36,
        qaOwner: '张三',
        caseStatus: 1, // 0: 未上传, 1: 待Review, 2: Review中, 3: 已Review, 4: 已修订
        reviewStatus: 0,
      },
      {
        id: 2230,
        title: '商品搜索功能测试用例',
        description: '',
        creator: 'Test QA',
        modifier: 'Test QA',
        gmtCreated: '2025-02-21T13:10:14.000+0800',
        gmtModified: '2025-02-21T16:45:20.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: 'REQ-2024-002',
        channel: 1,
        groupId: 1,
        recordNum: 0,
        companyId: 36,
        qaOwner: '李四',
        caseStatus: 2, // Review中
        reviewStatus: 1,
      },
      {
        id: 2226,
        title: '订单支付流程测试用例',
        description: '',
        creator: 'Test QA',
        modifier: 'Test QA',
        gmtCreated: '2025-02-18T13:08:19.000+0800',
        gmtModified: '2025-02-19T09:30:15.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: 'REQ-2024-003',
        channel: 1,
        groupId: 1,
        recordNum: 0,
        companyId: 36,
        qaOwner: '王五',
        caseStatus: 3, // 已Review
        reviewStatus: 2,
      },
      {
        id: 2225,
        title: '购物车管理测试用例',
        description: '',
        creator: 'Test QA',
        modifier: 'Test QA',
        gmtCreated: '2025-02-18T13:05:22.000+0800',
        gmtModified: '2025-02-18T17:20:10.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: 'REQ-2024-004',
        channel: 1,
        groupId: 1,
        recordNum: 0,
        companyId: 36,
        qaOwner: '赵六',
        caseStatus: 4, // 已修订
        reviewStatus: 2,
      },
      {
        id: 2217,
        title: '用户注册功能测试用例',
        description: '',
        creator: 'Test QA',
        modifier: 'Test QA',
        gmtCreated: '2025-02-10T16:04:21.000+0800',
        gmtModified: '2025-02-11T11:15:30.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: 'REQ-2024-005',
        channel: 1,
        groupId: 1,
        recordNum: 0,
        companyId: 36,
        qaOwner: '孙七',
        caseStatus: 0, // 未上传
        reviewStatus: 0,
      },
    ],
    total: 5,
  },
};

export { deleteFolderData, requirementViewData };
