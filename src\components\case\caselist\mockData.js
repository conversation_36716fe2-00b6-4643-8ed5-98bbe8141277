const deleteFolderData = {
  code: 200,
  msg: '服务运行成功',
  data: {
    dataSources: [
      {
        id: 2231,
        title: '222',
        description: '',
        creator: 'Test',
        modifier: 'Test',
        gmtCreated: '2025-02-21T14:35:53.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: '',
        channel: 1,
        groupId: 1,
        recordNum: 0,
        companyId: 36,
      },
      {
        id: 2230,
        title: '111111111111111',
        description: '',
        creator: 'Test',
        modifier: 'Test',
        gmtCreated: '2025-02-21T13:10:14.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: '',
        channel: 1,
        groupId: 1,
        recordNum: 0,
        companyId: 36,
      },
      {
        id: 2226,
        title: '66666',
        description: '',
        creator: 'Test',
        modifier: 'Test',
        gmtCreated: '2025-02-18T13:08:19.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: '',
        channel: 1,
        groupId: 1,
        recordNum: 0,
        companyId: 36,
      },
      {
        id: 2225,
        title: '22222222',
        description: '',
        creator: 'Test',
        modifier: 'Test',
        gmtCreated: '2025-02-18T13:05:22.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: '',
        channel: 1,
        groupId: 1,
        recordNum: 0,
        companyId: 36,
      },
      {
        id: 2217,
        title: 'LZC',
        description: '',
        creator: 'Test',
        modifier: 'Test',
        gmtCreated: '2025-02-10T16:04:21.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: '',
        channel: 1,
        groupId: 1,
        recordNum: 0,
        companyId: 36,
      },
      {
        id: 2216,
        title: '222',
        description: '',
        creator: 'Test',
        modifier: 'Test',
        gmtCreated: '2025-02-08T11:37:06.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: '333',
        channel: 1,
        groupId: 1,
        recordNum: 0,
        companyId: 36,
      },
      {
        id: 2212,
        title: '11',
        description: '',
        creator: 'Test',
        modifier: 'Test',
        gmtCreated: '2025-02-07T16:12:43.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: '11',
        channel: 1,
        groupId: 1,
        recordNum: 2,
        companyId: 36,
      },
      {
        id: 22311,
        title: '222',
        description: '',
        creator: 'Test',
        modifier: 'Test',
        gmtCreated: '2025-02-21T14:35:53.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: '',
        channel: 1,
        groupId: 1,
        recordNum: 0,
        companyId: 36,
      },
      {
        id: 22301,
        title: '111111111111111',
        description: '',
        creator: 'Test',
        modifier: 'Test',
        gmtCreated: '2025-02-21T13:10:14.000+0800',
        productLineId: 36,
        caseType: 0,
        requirementId: '',
        channel: 1,
        groupId: 1,
        recordNum: 0,
        companyId: 36,
      },
      
    ],
    total: 7,
  },
};

export { deleteFolderData };
